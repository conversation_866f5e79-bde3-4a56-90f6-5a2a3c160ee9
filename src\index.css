@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  font-family: "JetBrains Mono", monospace;
}
::-webkit-scrollbar {
  width: 0;
  height: 0;
}
pre {
  font-family: "JetBrains Mono", monospace;
}

@layer components {
  .tooltip {
    position: relative;
    display: inline-block;
  }
  .tooltip::after {
    content: attr(data-tooltip);
    visibility: hidden;
    opacity: 0;
    position: absolute;
    bottom: 115%;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(28, 28, 28, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.875rem;
    white-space: nowrap;
    transition: opacity 0.3s ease;
    z-index: 1;
  }
  .tooltip:hover::after {
    visibility: visible;
    opacity: 1;
  }
  .tooltip_canvas {
    pointer-events: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.625rem;
    white-space: nowrap;
    transition: opacity 0.3s ease;
    background-color: rgba(28, 28, 28, 0.9);
    color: white;
  }
}
