{"name": "personal_portfolio", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-three/drei": "^8.20.2", "@react-three/fiber": "^8.18.0", "devicons-react": "^1.4.1", "@types/node": "^24.0.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "three": "^0.149.0"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/three": "^0.177.0", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-swc": "^3.7.2", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.1"}}