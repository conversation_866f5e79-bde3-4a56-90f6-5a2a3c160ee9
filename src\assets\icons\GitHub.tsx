import { FC } from 'react'

type GitHubIconType = {
  size?: number,
  style?: string
}

const GitHubIcon:FC<GitHubIconType> = ({
  size=32,
  style
}) => {
  return (
    <svg className={style} width={size} height={size} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M16 2.66669C14.249 2.66669 12.5152 3.01156 10.8976 3.68163C9.27988 4.35169 7.81002 5.33382 6.57191 6.57193C4.07142 9.07242 2.66666 12.4638 2.66666 16C2.66666 21.8934 6.49333 26.8934 11.7867 28.6667C12.4533 28.7734 12.6667 28.36 12.6667 28V25.7467C8.97333 26.5467 8.18666 23.96 8.18666 23.96C7.57333 22.4134 6.70666 22 6.70666 22C5.49333 21.1734 6.8 21.2 6.8 21.2C8.13333 21.2934 8.84 22.5734 8.84 22.5734C10 24.6 11.96 24 12.72 23.68C12.84 22.8134 13.1867 22.2267 13.56 21.8934C10.6 21.56 7.49333 20.4134 7.49333 15.3334C7.49333 13.8534 8 12.6667 8.86666 11.72C8.73333 11.3867 8.26666 10 9 8.20002C9 8.20002 10.12 7.84002 12.6667 9.56002C13.72 9.26669 14.8667 9.12002 16 9.12002C17.1333 9.12002 18.28 9.26669 19.3333 9.56002C21.88 7.84002 23 8.20002 23 8.20002C23.7333 10 23.2667 11.3867 23.1333 11.72C24 12.6667 24.5067 13.8534 24.5067 15.3334C24.5067 20.4267 21.3867 21.5467 18.4133 21.88C18.8933 22.2934 19.3333 23.1067 19.3333 24.3467V28C19.3333 28.36 19.5467 28.7867 20.2267 28.6667C25.52 26.88 29.3333 21.8934 29.3333 16C29.3333 14.2491 28.9885 12.5152 28.3184 10.8976C27.6483 9.2799 26.6662 7.81004 25.4281 6.57193C24.19 5.33382 22.7201 4.35169 21.1024 3.68163C19.4848 3.01156 17.751 2.66669 16 2.66669Z" fill="#FFFFEE"/>
    </svg>
  )
}

export default GitHubIcon